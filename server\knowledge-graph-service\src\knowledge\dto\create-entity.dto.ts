import { IsString, <PERSON><PERSON>num, IsObject, Is<PERSON><PERSON>ber, <PERSON><PERSON><PERSON><PERSON>, <PERSON>, <PERSON> } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';
import { EntityType } from '../knowledge-graph.service';

export class CreateEntityDto {
  @ApiProperty({
    description: '实体类型',
    enum: EntityType,
    example: EntityType.EQUIPMENT,
  })
  @IsEnum(EntityType)
  type: EntityType;

  @ApiProperty({
    description: '实体名称',
    example: '数控机床-001',
  })
  @IsString()
  name: string;

  @ApiProperty({
    description: '实体属性',
    example: {
      model: 'CNC-2000',
      manufacturer: '某某机械',
      installDate: '2023-01-01',
    },
  })
  @IsObject()
  @IsOptional()
  properties?: Record<string, any>;

  @ApiProperty({
    description: '置信度',
    minimum: 0,
    maximum: 1,
    example: 0.9,
  })
  @IsNumber()
  @Min(0)
  @Max(1)
  confidence: number;

  @ApiProperty({
    description: '数据来源',
    example: 'manual_input',
  })
  @IsString()
  source: string;
}
