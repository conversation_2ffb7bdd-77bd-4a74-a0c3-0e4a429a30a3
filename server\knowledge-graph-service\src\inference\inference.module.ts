import { Modu<PERSON> } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { InferenceService } from './inference.service';
import { InferenceController } from './inference.controller';

// 实体
import { InferenceRule } from '../knowledge/entities/inference-rule.entity';
import { KnowledgeEntity } from '../knowledge/entities/knowledge-entity.entity';
import { KnowledgeRelation } from '../knowledge/entities/knowledge-relation.entity';

@Module({
  imports: [
    TypeOrmModule.forFeature([
      InferenceRule,
      KnowledgeEntity,
      KnowledgeRelation,
    ]),
  ],
  controllers: [InferenceController],
  providers: [InferenceService],
  exports: [InferenceService],
})
export class InferenceModule {}
