import { Modu<PERSON> } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { KnowledgeGraphService } from './knowledge-graph.service';
import { KnowledgeController } from './knowledge.controller';
import { KnowledgeGateway } from './knowledge.gateway';

// 实体
import { KnowledgeEntity } from './entities/knowledge-entity.entity';
import { KnowledgeRelation } from './entities/knowledge-relation.entity';
import { InferenceRule } from './entities/inference-rule.entity';

@Module({
  imports: [
    TypeOrmModule.forFeature([
      KnowledgeEntity,
      KnowledgeRelation,
      InferenceRule,
    ]),
  ],
  controllers: [KnowledgeController],
  providers: [
    KnowledgeGraphService,
    KnowledgeGateway,
  ],
  exports: [KnowledgeGraphService],
})
export class KnowledgeModule {}
