import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  Index,
} from 'typeorm';
import { EntityType } from '../knowledge-graph.service';

@Entity('knowledge_entities')
@Index(['type', 'name'])
@Index(['confidence'])
@Index(['source'])
export class KnowledgeEntity {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({
    type: 'enum',
    enum: EntityType,
    comment: '实体类型',
  })
  @Index()
  type: EntityType;

  @Column({
    type: 'varchar',
    length: 255,
    comment: '实体名称',
  })
  @Index()
  name: string;

  @Column({
    type: 'json',
    nullable: true,
    comment: '实体属性',
  })
  properties: Record<string, any>;

  @Column({
    type: 'decimal',
    precision: 3,
    scale: 2,
    default: 1.0,
    comment: '置信度',
  })
  confidence: number;

  @Column({
    type: 'varchar',
    length: 100,
    comment: '数据来源',
  })
  source: string;

  @Column({
    type: 'varchar',
    length: 255,
    nullable: true,
    comment: 'Neo4j节点ID',
  })
  neo4jId: string;

  @Column({
    type: 'text',
    nullable: true,
    comment: '描述信息',
  })
  description: string;

  @Column({
    type: 'json',
    nullable: true,
    comment: '元数据',
  })
  metadata: Record<string, any>;

  @Column({
    type: 'boolean',
    default: true,
    comment: '是否激活',
  })
  isActive: boolean;

  @CreateDateColumn({
    type: 'timestamp',
    comment: '创建时间',
  })
  createdAt: Date;

  @UpdateDateColumn({
    type: 'timestamp',
    comment: '更新时间',
  })
  updatedAt: Date;
}
