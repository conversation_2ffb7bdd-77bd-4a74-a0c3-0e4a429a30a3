// 知识图谱实体
import { KnowledgeEntity } from '../knowledge/entities/knowledge-entity.entity';
import { KnowledgeRelation } from '../knowledge/entities/knowledge-relation.entity';
import { InferenceRule } from '../knowledge/entities/inference-rule.entity';

// 导出所有实体
export const entities = [
  KnowledgeEntity,
  KnowledgeRelation,
  InferenceRule,
];

// 单独导出实体类
export {
  KnowledgeEntity,
  KnowledgeRelation,
  InferenceRule,
};
