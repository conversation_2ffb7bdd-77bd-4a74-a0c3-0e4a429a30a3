import { Injectable, Logger } from '@nestjs/common';
import { KnowledgeGraphService } from '../knowledge/knowledge-graph.service';

export interface ExpertRecommendation {
  id: string;
  type: 'maintenance' | 'troubleshooting' | 'optimization' | 'training';
  title: string;
  description: string;
  steps: string[];
  confidence: number;
  evidence: string[];
  relatedEntities: string[];
  estimatedTime: number;
  difficulty: 'low' | 'medium' | 'high';
  riskLevel: 'low' | 'medium' | 'high';
}

@Injectable()
export class ExpertService {
  private readonly logger = new Logger(ExpertService.name);

  constructor(
    private readonly knowledgeGraphService: KnowledgeGraphService,
  ) {}

  /**
   * 获取专家建议
   */
  async getExpertRecommendations(
    context: any,
    problemType: string,
  ): Promise<ExpertRecommendation[]> {
    try {
      this.logger.log(`获取专家建议: ${problemType}`);
      
      // 使用知识图谱服务获取建议
      const recommendations = await this.knowledgeGraphService.getExpertRecommendations(
        context,
        problemType,
      );

      return recommendations;
    } catch (error) {
      this.logger.error('获取专家建议失败', error);
      throw error;
    }
  }

  /**
   * 故障诊断建议
   */
  async getFaultDiagnosisRecommendations(
    equipmentId: string,
    symptoms: string[],
  ): Promise<ExpertRecommendation[]> {
    try {
      this.logger.log(`故障诊断: 设备 ${equipmentId}, 症状: ${symptoms.join(', ')}`);

      // 构建诊断上下文
      const context = {
        equipmentId,
        symptoms,
        type: 'fault_diagnosis',
      };

      // 获取诊断建议
      const recommendations = await this.getExpertRecommendations(context, 'troubleshooting');

      // 如果没有找到具体建议，提供通用建议
      if (recommendations.length === 0) {
        return this.getGenericFaultDiagnosisRecommendations(symptoms);
      }

      return recommendations;
    } catch (error) {
      this.logger.error('故障诊断失败', error);
      throw error;
    }
  }

  /**
   * 维护建议
   */
  async getMaintenanceRecommendations(
    equipmentId: string,
    maintenanceHistory: any[],
  ): Promise<ExpertRecommendation[]> {
    try {
      this.logger.log(`维护建议: 设备 ${equipmentId}`);

      const context = {
        equipmentId,
        maintenanceHistory,
        type: 'maintenance',
      };

      const recommendations = await this.getExpertRecommendations(context, 'maintenance');

      if (recommendations.length === 0) {
        return this.getGenericMaintenanceRecommendations();
      }

      return recommendations;
    } catch (error) {
      this.logger.error('获取维护建议失败', error);
      throw error;
    }
  }

  /**
   * 优化建议
   */
  async getOptimizationRecommendations(
    processId: string,
    performanceData: any,
  ): Promise<ExpertRecommendation[]> {
    try {
      this.logger.log(`优化建议: 流程 ${processId}`);

      const context = {
        processId,
        performanceData,
        type: 'optimization',
      };

      const recommendations = await this.getExpertRecommendations(context, 'optimization');

      if (recommendations.length === 0) {
        return this.getGenericOptimizationRecommendations();
      }

      return recommendations;
    } catch (error) {
      this.logger.error('获取优化建议失败', error);
      throw error;
    }
  }

  /**
   * 培训建议
   */
  async getTrainingRecommendations(
    skillGaps: string[],
    userProfile: any,
  ): Promise<ExpertRecommendation[]> {
    try {
      this.logger.log(`培训建议: 技能缺口 ${skillGaps.join(', ')}`);

      const context = {
        skillGaps,
        userProfile,
        type: 'training',
      };

      const recommendations = await this.getExpertRecommendations(context, 'training');

      if (recommendations.length === 0) {
        return this.getGenericTrainingRecommendations(skillGaps);
      }

      return recommendations;
    } catch (error) {
      this.logger.error('获取培训建议失败', error);
      throw error;
    }
  }

  /**
   * 通用故障诊断建议
   */
  private getGenericFaultDiagnosisRecommendations(symptoms: string[]): ExpertRecommendation[] {
    return [
      {
        id: `generic_fault_${Date.now()}`,
        type: 'troubleshooting',
        title: '通用故障诊断流程',
        description: '基于症状的标准诊断流程',
        steps: [
          '检查设备基本状态',
          '验证症状描述',
          '查看历史故障记录',
          '执行基础诊断测试',
          '分析可能原因',
          '制定修复方案',
        ],
        confidence: 0.6,
        evidence: symptoms,
        relatedEntities: [],
        estimatedTime: 60,
        difficulty: 'medium',
        riskLevel: 'low',
      },
    ];
  }

  /**
   * 通用维护建议
   */
  private getGenericMaintenanceRecommendations(): ExpertRecommendation[] {
    return [
      {
        id: `generic_maintenance_${Date.now()}`,
        type: 'maintenance',
        title: '标准维护流程',
        description: '设备标准维护检查流程',
        steps: [
          '检查设备外观',
          '清洁设备表面',
          '检查连接部件',
          '润滑活动部件',
          '检查安全装置',
          '记录维护结果',
        ],
        confidence: 0.7,
        evidence: [],
        relatedEntities: [],
        estimatedTime: 120,
        difficulty: 'low',
        riskLevel: 'low',
      },
    ];
  }

  /**
   * 通用优化建议
   */
  private getGenericOptimizationRecommendations(): ExpertRecommendation[] {
    return [
      {
        id: `generic_optimization_${Date.now()}`,
        type: 'optimization',
        title: '流程优化分析',
        description: '标准流程优化方法',
        steps: [
          '分析当前流程',
          '识别瓶颈环节',
          '评估改进机会',
          '制定优化方案',
          '实施改进措施',
          '监控优化效果',
        ],
        confidence: 0.6,
        evidence: [],
        relatedEntities: [],
        estimatedTime: 240,
        difficulty: 'high',
        riskLevel: 'medium',
      },
    ];
  }

  /**
   * 通用培训建议
   */
  private getGenericTrainingRecommendations(skillGaps: string[]): ExpertRecommendation[] {
    return [
      {
        id: `generic_training_${Date.now()}`,
        type: 'training',
        title: '技能提升培训计划',
        description: `针对 ${skillGaps.join('、')} 的培训建议`,
        steps: [
          '评估当前技能水平',
          '制定学习计划',
          '选择培训资源',
          '开始理论学习',
          '进行实践练习',
          '评估学习效果',
        ],
        confidence: 0.7,
        evidence: skillGaps,
        relatedEntities: [],
        estimatedTime: 480,
        difficulty: 'medium',
        riskLevel: 'low',
      },
    ];
  }
}
